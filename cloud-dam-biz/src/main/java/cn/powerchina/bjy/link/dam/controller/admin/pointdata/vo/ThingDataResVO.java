package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 监测图形-过程线-分量信息-分量值
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线-分量信息-分量值")
@Data
public class ThingDataResVO {

    @Schema(description = "分量值")
    private BigDecimal thingValue;

    @Schema(description = "测值时间")
    private LocalDateTime pointTime;
}